/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-09-14
 * @Description:
 */
module.exports = {
  env: {
    node: true,
    es2021: true
  },
  extends: [
    '@ecomfe/eslint-config',
    '@ecomfe/eslint-config/typescript'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module'
  },
  plugins: ['@typescript-eslint'],
  rules: {
    'comma-dangle': [2, 'never'],
    'indent': ['error', 2]
  }
};
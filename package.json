{"name": "icon-server", "version": "0.0.1", "description": "百度智能云icon管理平台server", "main": "app.ts", "scripts": {"dev": "NODE_ENV=localDev nodemon app.ts", "lint": "eslint './src/**/*.{js,ts,tsx}' --quiet"}, "author": "", "license": "ISC", "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "dependencies": {"@babel/eslint-plugin": "^7.14.5", "@baiducloud/sdk": "^1.0.0-rc.31", "@ecomfe/eslint-config": "^7.3.0", "@types/adm-zip": "^0.4.34", "@types/fs-extra": "^9.0.12", "@types/ioredis": "^4.27.2", "@types/koa": "^2.13.4", "@types/koa-router": "^7.4.4", "@types/lodash": "^4.14.172", "@types/qs": "^6.9.7", "@types/svgo": "^2.4.1", "@types/uuid": "^8.3.1", "@types/validator": "^13.6.3", "@typescript-eslint/eslint-plugin": "^5.35.1", "@typescript-eslint/parser": "^5.35.1", "adm-zip": "^0.5.6", "axios": "^0.21.4", "dayjs": "^1.10.6", "fs-extra": "^10.0.0", "ioredis": "^4.27.9", "joi": "^17.4.2", "koa": "^2.13.1", "koa-body": "^4.2.0", "koa-router": "^10.1.1", "lodash": "^4.17.21", "mysql2": "^2.3.0", "pm2": "^5.2.0", "qs": "^6.10.1", "reflect-metadata": "^0.1.13", "sequelize": "^6.6.5", "sequelize-typescript": "^2.1.0", "svgo": "^2.6.0", "svgson": "^5.2.1", "ts-node": "^10.2.1", "tsconfig-paths": "^3.11.0", "typescript": "^4.3.5", "url": "^0.11.0", "uuid": "^8.3.2", "winston": "^3.3.3", "winston-daily-rotate-file": "^4.5.5"}, "devDependencies": {"eslint": "^7.32.0", "husky": "^4.3.8", "lint-staged": "^11.1.2", "nodemon": "^2.0.12"}}
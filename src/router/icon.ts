/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2021-08-31 16:10:10
 */

import {iconCtrl} from '../controller';
import iconSchema from '../schema/iconSchema';
import IRoute from '../types/routeType';

const routes: IRoute[] = [
  // 文件上传
  {
    method: 'post',
    path: '/icon/upload',
    controller: iconCtrl.upload,
    schema: iconSchema.uploadSvgSchema
  },
  // zip压缩包上传
  {
    method: 'post',
    path: '/icon/zipUpload',
    controller: iconCtrl.zipUpload,
    schema: iconSchema.uploadSvgSchema
  },
  // 绑定上传资源到项目中
  {
    method: 'post',
    path: '/icon/resourceBind',
    controller: iconCtrl.bind,
    schema: iconSchema.bindSchema
  },
  // 查询iconlist
  {
    method: 'post',
    path: '/icon/list',
    controller: iconCtrl.iconlist,
    schema: iconSchema.iconlistSchema
  },
  // icon下载
  {
    method: 'post',
    path: '/icon/download',
    controller: iconCtrl.iconDownload,
    schema: iconSchema.downloadSchema
  },
  // icon批量删除
  {
    method: 'post',
    path: '/icon/batchDelete',
    controller: iconCtrl.batchDelete,
    schema: iconSchema.batchDelSchema
  },
  // icon批量删除
  {
    method: 'post',
    path: '/icon/edit',
    controller: iconCtrl.editIcon,
    schema: iconSchema.iconEditParam
  },
  // 从别的项目添加
  {
    method: 'post',
    path: '/icon/addFromOtherProject',
    controller: iconCtrl.fromProjectAdd
  }
];

export default routes;
/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2021-08-31 13:27:46
 */

import {projectCtrl} from '../controller';
import {project} from '../schema';
import IRoute from '../types/routeType';

const routes: IRoute[] = [
  {
    method: 'get',
    path: '/project/detail/:id',
    controller: projectCtrl.getProject,
    schema: project.getProjectSchema
  },
  {
    method: 'get',
    path: '/project/list',
    controller: projectCtrl.getProjectList
  },
  {
    method: 'post',
    path: '/project/create',
    controller: projectCtrl.createProject,
    schema: project.createProjectSchema
  },
  {
    method: 'post',
    path: '/project/:id/edit',
    controller: projectCtrl.editProject,
    schema: project.getProjectSchema
  },
  {
    method: 'post',
    path: '/project/:id/delete',
    controller: projectCtrl.deleteProject,
    schema: project.getProjectSchema
  },
  {
    method: 'get',
    path: '/project/permission/:id',
    controller: projectCtrl.getProjectInfoWithPermission,
    schema: project.getProjectSchema
  },
  {
    method: 'post',
    path: '/project/permission/add/:id',
    controller: projectCtrl.addPermissionForProject,
    schema: project.getProjectSchema
  },
  {
    method: 'post',
    path: '/project/permission/delete/:id',
    controller: projectCtrl.deletePermissionForProject,
    schema: project.getProjectSchema
  },
  {
    method: 'post',
    path: '/project/permission/edit/:id',
    controller: projectCtrl.editPermissionForProject,
    schema: project.getProjectSchema
  },
  {
    method: 'post',
    path: '/project/:id/type/add',
    controller: projectCtrl.addTypeForProject,
    schema: project.getProjectSchema
  },
  {
    method: 'post',
    path: '/project/:id/type/delete',
    controller: projectCtrl.deleteTypeForProject,
    schema: project.getProjectSchema
  },
  {
    method: 'post',
    path: '/project/:id/type/edit',
    controller: projectCtrl.editTypeForProject,
    schema: project.getProjectSchema
  },
  {
    method: 'post',
    path: '/project/:id/type/sort',
    controller: projectCtrl.sortTypeForProject,
    schema: project.getProjectSchema
  }
];

export default routes;
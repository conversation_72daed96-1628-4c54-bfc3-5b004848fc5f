/*
 * @Description: 路由聚合
 * @Author: <EMAIL>
 * @Date: 2021-08-26 15:40:13
 */

import Router from 'koa-router';
import fs from 'fs-extra';
import paramCheck from '../middleware/paramCheck';
import config from '../config';
import RouteType from '../types/routeType';

const router = new Router({prefix: config.prefix});

const registerRouter = (routers: RouteType[]) => {
  routers.forEach(item => {
    const {method, path, schema, controller, permission} = item;
    // 权限控制待补充
    if (permission) {
      // router[method](item.path, paramCheck(schema), item.controller);
    }
    else {
      router[method](path, paramCheck(schema!), controller);
    }
  });
};

const routes = fs.readdirSync(__dirname);
routes.forEach(async item => {
  if (item !== 'index.ts') {
    const module = await import(`./${item}`);
    registerRouter(module.default);
  }
});

export default router;
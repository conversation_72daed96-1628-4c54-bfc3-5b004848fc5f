import {Optional} from 'sequelize';
import {Table, Model, Column, <PERSON>Key, AutoIncrement} from 'sequelize-typescript';

interface UserRoleInfo {
  id: number;
  user_id: number;
  project_id: number;
  role: number;
}

type UserRoleExclude = 'id' | 'role';

type UserRoleCreationAttr = Optional<UserRoleInfo, UserRoleExclude>;

@Table({
  tableName: 'user_role',
  timestamps: false
})
export default class UserRole extends Model<UserRoleInfo, UserRoleCreationAttr> {
  @PrimaryKey
  @AutoIncrement
  @Column
  id!: number;

  @Column
  user_id!: number;

  @Column
  project_id!: number;

  @Column
  role!: number;
}


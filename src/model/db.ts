/*
 * @Description: 数据库连接
 * @Author: wang<PERSON><EMAIL>
 * @Date: 2021-08-27 16:24:41
 */

import {Sequelize} from 'sequelize-typescript';
import config from '../config';
import path from 'path';
// import fs from 'fs-extra';

const {mysql} = config;

const seq = new Sequelize(
  mysql.database,
  mysql.user,
  mysql.password,
  {
    host: mysql.host,
    dialect: 'mysql',
    port: mysql.port
  }
);
const resolve = (pathStr: string) => path.resolve(__dirname, pathStr);
seq.addModels([
  resolve('./icon.ts'),
  resolve('./project.ts'),
  resolve('./type.ts'),
  resolve('./user.ts'),
  resolve('./user-role.ts')
]);

export default seq;

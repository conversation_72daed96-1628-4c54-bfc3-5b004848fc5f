/*
 * @Description: type Model
 * @Author: <EMAIL>
 * @Date: 2021-09-13 18:36:44
 */
import {Optional} from 'sequelize';
import {Table, Model, Column, PrimaryKey, AutoIncrement, Comment, ForeignKey} from 'sequelize-typescript';
import Project from './project';

export interface TypeAttr {
  id: number;
  name: string;
  project_id: number;
  parent_id: number;
  iconType: number;
  sort: number;
}

type TypeExclude = 'id' | 'parent_id';


type TypeCreationAttr = Optional<TypeAttr, TypeExclude>;

@Table({
  tableName: 'type',
  timestamps: false
})
export default class Type extends Model<Type, TypeCreationAttr> {
  // 分组id
  @PrimaryKey
  @AutoIncrement
  @Column
  id!: number;

  // 分类名称
  @Column
  name!: string;

  @ForeignKey(() => Project)
  // 所属项目id
  @Column
  project_id!: number;

  // 父节点id
  @Column
  parent_id!: number;


  @Comment('图标类型：0——standardSvg；1——PNG；2——SVG')
  @Column
  iconType!: number;

  @Column
  sort!: number;
}
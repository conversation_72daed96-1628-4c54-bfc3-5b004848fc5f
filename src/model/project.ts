/*
 * @Description: project Model
 * @Author: <EMAIL>
 * @Date: 2021-08-31 11:24:03
 */

import {Optional} from 'sequelize';
import {Table, Model, Column, PrimaryKey, AutoIncrement, HasMany} from 'sequelize-typescript';
import Type from './type';

export interface ProjectAttr {
    id: number;
    name: string;
    description: string;
    deleted: boolean;
    create_time: Date;
    update_time: Date;
}

type ProjectExclude = 'id' | 'deleted' | 'create_time' | 'update_time';


type ProjectCreationAttr = Optional<ProjectAttr, ProjectExclude>;

@Table({
  tableName: 'project',
  timestamps: false
})
export default class Project extends Model<ProjectAttr, ProjectCreationAttr> {
  @PrimaryKey
  @AutoIncrement
  @Column
  id!: number;

  @Column
  name!: string;

  @Column
  description!: string;

  @Column
  deleted!: boolean;

  @Column
  create_time!: Date;

  @Column
  update_time!: Date;

  @HasMany(() => Type, 'project_id')
  types!: Type[];
}
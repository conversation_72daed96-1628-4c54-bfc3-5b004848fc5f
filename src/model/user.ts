import {Optional} from 'sequelize';
import {Table, Model, Column, PrimaryKey, AutoIncrement} from 'sequelize-typescript';

type UserExclude = 'id' | 'name' | 'username' | 'email';

type UserCreationAttr = Optional<UserInfo, UserExclude>;

@Table({
  tableName: 'user',
  timestamps: false
})
export default class User extends Model<UserInfo, UserCreationAttr> {
  // 分组id
  @PrimaryKey
  @AutoIncrement
  @Column
  id!: number;

  // 姓名
  @Column
  name!: string;

  // 用户名
  @Column
  username!: string;

  // 邮箱
  @Column
  email!: string;
}


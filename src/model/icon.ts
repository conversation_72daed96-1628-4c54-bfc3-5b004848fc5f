/*
 * @Description: icon model
 * @Author: <EMAIL>
 * @Date: 2021-09-17 14:12:20
 */

import {Table, Model, Column, PrimaryKey, AutoIncrement, Default, Comment} from 'sequelize-typescript';
import {Optional} from 'sequelize/types';

export interface IconAttr {
  id: number;
  projectId: number;
  typeId: number;
  name: string;
  enName: string;
  icon: string;
  path?: string;
  size: number;
  type: number;
  color: string;
  strokeWidth: number;
  strokeLinecap: number;
  strokeLinejoin: number;
  deleted: number;
  create_time: Date;
  update_time: Date;
  iconType: number;
}

type IconExclude = 'id' | 'size' | 'type' | 'color' | 'strokeWidth' | 'strokeLinecap'
| 'strokeLinejoin' | 'deleted' | 'create_time' | 'update_time' | 'iconType';

type IconCreationAttr = Optional<IconAttr, IconExclude>;

@Table({
  tableName: 'icon',
  timestamps: false,
  freezeTableName: true,
  underscored: true
})
export default class Icon extends Model<IconAttr, IconCreationAttr> {
  @PrimaryKey
  @AutoIncrement
  @Comment('主键id')
  @Column
  id!: number;

  @Comment('所属项目id')
  @Column
  projectId!: number;

  @Comment('所属分类id')
  @Column
  typeId!: number;

  @Comment('名称,用于查询')
  @Column
  name!: string;

  @Comment('英文名称，在所属项目中唯一')
  @Column
  enName!: string;

  @Comment('图标原文件')
  @Column
  icon!: string;

  @Comment('解析用于组装的路径')
  @Column
  path!: string;

  @Comment('图标大小')
  @Default(16)
  @Column
  size!: number;

  @Comment('图标类型 0: 线性 1:面性')
  @Default(0)
  @Column
  type!: number;

  @Comment('图标颜色')
  @Column
  color!: number;

  @Comment('线段粗细')
  @Default(1)
  @Column
  strokeWidth!: number;

  @Comment('端点类型 0：圆角 1：平角 2：方角')
  @Default(0)
  @Column
  strokeLinecap!: number;

  @Comment('拐点类型 0：圆角 1：平角 2：方角')
  @Default(0)
  @Column
  strokeLinejoin!: number;

  @Comment('是否删除')
  @Default(0)
  @Column
  deleted!: number;

  @Comment('图标类型：0——standardSvg；1——png；2——SVG；2——illustrationPng')
  @Default(0)
  @Column
  iconType!: number;

  @Comment('创建时间')
  @Column
  create_time!: Date;

  @Comment('最后更新时间')
  @Column
  update_time!: Date;
}
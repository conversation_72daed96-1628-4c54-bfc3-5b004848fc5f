/*
 * @Description: 工具方法
 * @Author: <EMAIL>
 * @Date: 2021-09-30 14:28:52
 */

/**
  * 将百度翻译结果转换成驼峰格式
  * @param str 翻译结果
  * @returns 驼峰格式
  */
export const transCamelCase = (str: string) => {
  let pattern = /(\_|\-|\s)(\w)/g;
  let result = str.replace(pattern, (match, p1, p2) => p2.toUpperCase());
  // 百度翻译 xx_中文 => xx_ Chinese会带上空格，再进行一次处理
  if (pattern.test(result)) {
    return result.replace(pattern, (match, p1, p2) => p2.toUpperCase());
  }
  return result;
};

/**
 * 自定义错误类
 * @returns {
 *  msg: 错误提示信息,
 *  code: 错误码
 *  （302 uuap认证失败，重定向)
 * }
 */
export class RedirectException extends Error {
  code: number;
  constructor(msg: string) {
    super(msg);
    this.code = 302;
  }
}

export function filterArg(arg: any[]) {
  return arg.filter(item => !(item?.name === 'bound dispatch'));
}
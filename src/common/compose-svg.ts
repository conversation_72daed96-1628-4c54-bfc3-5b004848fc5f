/*
 * @Description: 组装svg
 * @Author: qin<PERSON>yan
 * @Date: 2021-09-15
 */
import {INode, stringify} from 'svgson';
import {optimize} from 'svgo';
import {Path, Line, Filled} from './parse-svg';

// 图标风格
export type IconType = 'line' | 'filled';

// 端点类型
export type StrokeLinecapType = 'round' | 'butt' | 'square';

// 拐点类型
export type StrokeLinejoinType = 'round' | 'miter' | 'bevel';

// 全局样式
export interface GlobalStyle {
  size: number; // 图标大小
  color: string; // 图标颜色
  strokeWidth: number; // 线框粗细
  strokeLinecap: StrokeLinecapType;
  strokeLinejoin: StrokeLinejoinType;
}

export default class ComposeSvg {
  globalStyle: GlobalStyle;
  type: IconType;
  pathStyles: Path[] = [];

  constructor(type: IconType, globalStyle: GlobalStyle) {
    this.globalStyle = globalStyle;
    this.type = type;
  }

  /**
   * 整合svg样式
   * @param sourceSvg 原svg字符
   * @param pathStyles svg样式
   * @returns {SvgString} 整合后的图标
   */
  async composeSvg(svgJson: INode, pathStyles: Path[]) {
    this.pathStyles = pathStyles;
    this.scanSvg(svgJson);
    if (svgJson.attributes) {
      svgJson.attributes.width = String(this.globalStyle.size) + 'px';
      svgJson.attributes.height = String(this.globalStyle.size) + 'px';
    }
    const svgString = stringify(svgJson);
    const newSvg = optimize(svgString, {}).data;
    return newSvg;
  }

  /**
   * 递归遍历svg路径
   * @param svgNode svg节点
   */
  scanSvg(svgNode: any) {
    if (svgNode.children?.length > 0) {
      const svgChildrenNode = svgNode.children;
      for (let i = 0; i < svgChildrenNode?.length;) {
        const isDelete = this.scanSvg(svgChildrenNode[i]);
        // 删除隐藏的路径
        if (isDelete) {
          svgChildrenNode.splice(i, 1);
        }
        else {
          i++;
        }
      }
    }
    else {
      // id为0的不做处理
      if (svgNode.attributes?.id && svgNode.attributes.id !== '0' && svgNode.tagName !== 'g') {
        const path = this.findStylePath(svgNode.attributes.id);
        if (path.hidden) {
          return true;
        }
        svgNode.attributes = this.setPathStyle(svgNode.attributes, path);
      }
    }
  }

  /**
   * 设置路径样式
   * @param attributes 路径属性
   * @param path 路径风格
   * @returns 新的路径属性
   */
  setPathStyle(attributes: any, pathStyle: Line | Filled) {
    let color = this.globalStyle.color || pathStyle.color;
    // 反色，颜色默认为白色
    // @ts-ignore
    if (pathStyle.invertColor) {
      color = '#fff';
    }
    // 需要描边
    if (pathStyle.line) {
      attributes.stroke = color;
      attributes['stroke-width'] = String(this.globalStyle.strokeWidth);
      attributes['stroke-opacity'] === '0' && (attributes['stroke-opacity'] = '');
      attributes.opacity === '0' && (attributes.opacity = '');
    }
    // 没有描边
    if (!pathStyle.line) {
      attributes.stroke = 'none';
    }
    // 需要填充
    if (pathStyle.filled) {
      attributes.fill = color;
      attributes['fill-opacity'] === '0' && (attributes['fill-opacity'] = '');
      attributes.opacity === '0' && (attributes.opacity = '');
    }
    // 没有填充
    if (!pathStyle.filled) {
      attributes.fill = 'none';
    }
    // 设置端点和拐点
    attributes['stroke-linecap'] = this.globalStyle.strokeLinecap;
    attributes['stroke-linejoin'] = this.globalStyle.strokeLinejoin;
    return attributes;
  }

  /**
   * 查找路径样式
   * @param id 路径ID
   * @returns 路径样式
   */
  findStylePath(id: string) {
    const idNum = Number(id.split('-')[0]);
    const path = this.pathStyles.find(path => idNum === path.id) as Path;
    return path[this.type];
  }
}

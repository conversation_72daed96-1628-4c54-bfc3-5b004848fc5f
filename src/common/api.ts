/*
 * @Description: 第三方接口请求
 * @Author: <EMAIL>
 * @Date: 2021-09-14 19:23:42
 */

import axios from 'axios';
import config from '../config';
import log from './log';
import crypto from 'crypto';

// 百度翻译接口
export const getTranslate = (name: string) => {
  const {appId, key, host} = config.fanyi;
  const salt = Date.now().toString();
  const from = 'zh';
  const to = 'en';
  const md5 = crypto.createHash('md5');
  md5.update(Buffer.from(appId + name + salt + key));
  const sign = md5.digest('hex');
  const path = '/api/trans/vip/translate'
  + `?q=${encodeURIComponent(name)}&from=${from}&to=${to}&appid=${appId}&salt=${salt}&sign=${sign}`;
  return axios({
    baseURL: host,
    url: path,
    method: 'get'
  }).then(({data}) => {
    if (data.error_code) {
      // 翻译额度用完时，翻译结果为空
      return [];
      // log.error('fanyi err:', {
      //   message: `code: ${data.error_code} ${data.error_msg}`
      // });
      // throw new Error('get en_name fail');
    }
    else {
      return data.trans_result.map((item: any) => item.dst);
    }
  });
};
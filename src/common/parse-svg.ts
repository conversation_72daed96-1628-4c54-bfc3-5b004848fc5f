/*
 * @Description: 解析SVG
 * @Author: qin<PERSON>yan
 * @Date: 2021-09-15
 */
import {INode, parse} from 'svgson';

export interface Line {
  line: boolean; // 支持描边
  filled: boolean; // 支持填充
  color: string; // 颜色
  hidden: boolean; // 隐藏路径
}

export interface Filled extends Line{
  invertColor: boolean; // 填充反色
}

export interface Path {
  id: number; // 路径id
  line: Line; // 线性下的属性
  filled: Filled; // 面性下的属性
};

export interface ParseReturn {
  success: boolean;
  path: Path[];
  svgjson: INode;
}

/**
 * 解析规则
 * @param rule
 * @param color
 * @returns
 */
export function parseRule(rule: string, color: string = '#000') {
  const res: Filled | Line = {
    invertColor: false,
    line: false,
    filled: false,
    color: color,
    hidden: false
  };
  rule = rule.toLocaleUpperCase();
  res.line = rule.includes('L');
  res.filled = rule.includes('F');
  res.invertColor = rule.includes('W');
  res.hidden = rule.includes('N');

  return res;
}

/**
 * 拆解id
 * @param idStr
 * @param color
 * @returns
 */
export function splitId(idStr: string, color: string): Path {
  const props = idStr.split('-');
  const id = Number(props[0]);
  return {
    id,
    filled: parseRule(props[1], color),
    line: parseRule(props[2] || 'L', color)
  };
}

/**
 * 递归读取svg路径信息
 * @param svgNode
 * @param svgPaths
 */
export function readId(svgNode: INode, svgPaths: Path[]) {
  const nodeId = svgNode.attributes.id;
  if (svgNode.children?.length > 0) {
    svgNode.children.forEach((item: any) => {
      readId(item, svgPaths);
    });
  }
  else {
    // id为0的不做处理
    if (nodeId && nodeId !== '0' && svgNode.name !== 'g') {
      const idStr = nodeId.split('-');
      // 非标准格式svgid描述规则
      if (!idStr[1]) {
        return;
      }
      svgPaths.push(
        splitId(
          nodeId,
          svgNode.attributes.fill || svgNode.attributes.stroke
        )
      );
    }
  }
}

/**
 * 解析svg
 * @param svg  svg字符串
 * @returns {Path[]} 解析后的路径信息
 */
export async function parseSvg(svg: string): Promise<ParseReturn> {
  const svgjson = await parse(svg);
  const svgPaths: Path[] = [];
  readId(svgjson, svgPaths);
  if (!svgPaths.length) {
    return {
      success: false,
      svgjson,
      path: svgPaths
    };
  }
  return {
    success: true,
    svgjson,
    path: svgPaths
  };
}

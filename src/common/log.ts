/*
 * @Description: 日志
 * @Author: <EMAIL>
 * @Date: 2021-08-24 20:33:10
 */

import DailyRotateFile from 'winston-daily-rotate-file';
import {createLogger, transports, format} from 'winston';
import config from '../config';

const {combine, timestamp, printf} = format;

let trans = null;
if (process.env.NODE_ENV === 'production') {
  trans = [new DailyRotateFile(config.logger.prod)];
}
else {
  trans = [new transports.Console(config.logger.dev)];
}

const myFormat = printf(info => {
  const {level, message, timestamp} = info;
  return `[${timestamp}]-[${level}]-[${process.pid}]: ${message}`;
});

const logger = createLogger({
  transports: trans,
  defaultMeta: {service: 'icon-service'},
  format: combine(
    timestamp({
      format: 'YYYY-MM-DD HH:mm:ss:SSS'
    }),
    myFormat
  )
});

export default logger;
/*
 * @Description: bos服务
 * @Author: <EMAIL>
 * @Date: 2021-09-10 15:14:29
 */

import {BosClient} from '@baiducloud/sdk';
import config from '../config';

const {bos} = config;

class Bos {
    client: any;
    constructor() {
      this.client = new BosClient({
        credentials: {
          ak: bos.ak,
          sk: bos.sk
        },
        endpoint: bos.endpoint
      });
    }

    /**
     * 资源存储文件夹 console-center/icon-plat/resource/
     * @param filename  文件名称,需要带后缀
     * @returns {string} 组装的链接
     */
    async putResourceObjectFromString(filename: string, buffer: Buffer) {
      await this.client.putObjectFromString(
        bos.bucket,
        `icon-plat/${filename}`,
        buffer
      );
      return `https://${bos.bucket}.${bos.region}/icon-plat/${filename}`;
    }

    // 获取Object
    getObject(key: string) {
      return this.client.getObject(bos.bucket, key);
    }

    // 列举Object
    listObject(key: string) {
      return this.client.listObjects(bos.bucket, key);
    }
}

export const bosClient = new Bos();
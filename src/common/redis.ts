/*
 * @Description: redis
 * @Author: <EMAIL>
 * @Date: 2021-09-10 14:37:06
 */

import IoRedis from 'ioredis';
import log from '../common/log';
import config from '../config';

class Redis {
    redis: IoRedis.Redis;
    constructor() {
      let redisConfig = config.redis;
      this.redis = new IoRedis({
        host: redisConfig.host,
        port: redisConfig.port
      });
    }

    /**
     *  存储string类型
     *
     * @param key
     * @param value
     * @param expire 过期时间默认2小时
     */
    async setStringKey(key: string, value: IoRedis.ValueType, expire = 7200) {
      try {
        const res = await this.redis.set(key, value, 'EX', expire);
        return res;
      }
      catch (e) {
        log.warn('redis err', {message: e});
      }
    }

    // 获取string类型
    async getStringKey(key: string) {
      try {
        const res = await this.redis.get(key);
        return res;
      }
      catch (e) {
        log.warn('redis err', {message: e});
      }
    }

    // 以buffer形式获取
    async getBufferKey(key: string) {
      try {
        const res = await this.redis.getBuffer(key);
        return res;
      }
      catch (e) {
        log.warn('redis err', {message: e});
      }
    }
}

export const redisClient = new Redis();

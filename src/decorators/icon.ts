import fs from 'fs-extra';
import {transCamelCase, filterArg} from '../util/util';
import {getTranslate} from '../common/api';
import {Icon} from '../model';
import {intersection} from 'lodash';

export function iconUpload(iconType: 'png' | 'svg+xml') {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    descriptor.value = async function (...arg: any[]) {
      const [payload] = arg;
      if (payload.file) {
        const {name, size, type, path} = payload.file;
        if (payload.file) {
          if (type !== 'image/' + iconType) {
            throw new Error(`非法${iconType}文件！`);
          }
          else if (size > 200 * 1024 * 1024) {
            throw new Error('文件大小限制2m!');
          }
          else {
            const filePromise = new Promise<Buffer>((resolve, reject) => {
              const reader = fs.createReadStream(path);
              let arr: Buffer[] = [];
              reader.on('data', data => {
                arr.push(data as Buffer);
              });
              reader.on('end', () => {
                resolve(Buffer.concat(arr));
              });
              reader.on('error', () => {
                reject();
              });
            });
            const buffer = await filePromise;
            let originName = name?.split('.')[0];
            const enNameList = await getTranslate(originName);
            let enName = transCamelCase(enNameList[0] || '');
            const result = await method.apply(this, [...filterArg(arg), buffer]);
            return Object.assign(result, {
              name: originName,
              enName
            });
          }
        }
      }
      else {
        throw new Error('upload fail');
      }
    };
  };
}

export function iconBind() {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    descriptor.value = async function (...arg: any[]) {
      const [payload] = arg;
      let {projectId, iconList} = payload;
      if (iconList.length > 40) {
        throw new Error('单次上传不能超过40个');
      }
      const enList = await Icon.findAll({
        where: {
          projectId
        },
        attributes: ['enName']
      });
      // 项目中enName不能重复,提示项目中所有的重复的英文名
      const repeatList = intersection(enList.map(item => item.enName), iconList.map((list: any) => list.enName));
      if (repeatList.length) {
        throw new Error(`以下英文名称在项目中重复：${repeatList.join(',')}`);
      }
      return method.apply(this, [...filterArg(arg)]);
    };
  };

}
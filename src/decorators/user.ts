import {User} from '../model/index';
import {filterArg} from '../util/util';

export function user() {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    descriptor.value = async function (...arg: any[]) {
      const [ctx] = arg;
      const referer = String(ctx?.request?.header?.referer || '');
      let user: any = {};
      // 云舍来源的，不验证登陆
      if (referer && (referer.includes('yunshe.baidu-int.com') || referer.includes('sandbox090.gzns.baidu.com'))) {
        user = {};
      }
      else {
        const {userInfo} = ctx;
        user = await User.findOne({
          where: {
            email: userInfo.email
          }
        });
      }
      return await method.apply(this, [...filterArg(arg), user]);
    };
  };
}
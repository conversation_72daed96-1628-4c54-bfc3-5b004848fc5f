/*
 * @Description: 开发配置
 * @Author: wang<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2021-08-25 10:22:04
 */

export default {
  // uuap: {
  //   protocol: 'https:',
  //   hostname: 'itebeta.baidu.com',
  //   port: '443',
  //   loginMethod: '/login',
  //   logoutMethod: '/logout',
  //   sessionValidateUrl: '/session/validate',
  //   sTokenDecryptMethod: '/sTokenDecrypt',
  //   appKey: 'uuapclient-459689290222620672-pcovi',
  //   secretKey: 'dd38ac93ee6d4209a12b20',
  //   version: 'v2',
  //   pTokenName: 'UUAP_P_TOKEN_OFFLINE',
  //   sTokenName: 'UUAP_S_TOKEN_OFFLINE'
  // },
  uuap: {
    protocol: 'https:',
    hostname: 'itebeta.baidu.com',
    port: '443',
    loginMethod: '/login',
    logoutMethod: '/logout',
    validateMethod: '/session/validate',
    sTokenDecryptMethod: '/sTokenDecrypt',
    appKey: 'uuapclient-459689290222620672-pcovi',
    secretKey: 'dd38ac93ee6d4209a12b20',
    version: 'v2',
    pTokenName: 'UUAP_P_TOKEN_OFFLINE',
    sTokenName: 'UUAP_S_TOKEN'
  },
  mysql: {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: '123456',
    database: 'bce_icon_service'
  }
};
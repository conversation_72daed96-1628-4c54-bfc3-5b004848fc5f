/*
 * @Description: 配置输出
 * @Author: <EMAIL>
 * @Date: 2021-08-25 17:29:45
 */
import baseConfig from './default';
import devConfig from './development';
import prodConfig from './production';
import localConfig from './localDev';

import _ from 'lodash';
let mergeConfig = baseConfig;
if (process.env.NODE_ENV === 'development') {
  mergeConfig = _.merge(baseConfig, devConfig);
}
else if (process.env.NODE_ENV === 'production') {
  mergeConfig = _.merge(baseConfig, prodConfig);
}
else if (process.env.NODE_ENV === 'localDev') {
  mergeConfig = _.merge(baseConfig, localConfig);
}

export default mergeConfig;

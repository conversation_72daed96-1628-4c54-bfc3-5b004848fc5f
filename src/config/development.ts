/*
 * @Description: 开发配置
 * @Author: wangshu<PERSON><PERSON>@baidu.com
 * @Date: 2021-08-25 10:22:04
 */

export default {
  // uuap: {
  //   protocol: 'https:',
  //   hostname: 'itebeta.baidu.com',
  //   port: '443',
  //   loginMethod: '/login',
  //   logoutMethod: '/logout',
  //   sessionValidateUrl: '/session/validate',
  //   sTokenDecryptMethod: '/sTokenDecrypt',
  //   appKey: 'uuapclient-459689290222620672-pcovi',
  //   secretKey: 'dd38ac93ee6d4209a12b20',
  //   version: 'v2',
  //   pTokenName: 'UUAP_P_TOKEN_OFFLINE',
  //   sTokenName: 'UUAP_S_TOKEN_OFFLINE'
  // },
  uuap: {
    protocol: 'https:',
    hostname: 'uuap.baidu.com',
    port: '443',
    loginMethod: '/login',
    logoutMethod: '/logout',
    sessionValidateUrl: '/session/validate',
    sTokenDecryptMethod: '/sTokenDecrypt',
    appKey: 'uuapclient-6-zgXaWmA27L0mQYmabte3',
    secretKey: 'b2f9e27bc8794dc0b947ad',
    version: 'v2',
    pTokenName: 'UUAP_P_TOKEN',
    sTokenName: 'UUAP_S_TOKEN_SANDBOX'
  },
  // 云桥版本
  // mysql: {
  //   host: '*************',
  //   port: 8005,
  //   user: 'bce-sandbox',
  //   password: '********************************',
  //   database: 'bce_icon_service'
  // },
  // 服务平台版本
  mysql: {
    host: '*************',
    port: 8005,
    user: 'bce_sandbox',
    password: '********************************',
    database: 'bce_icon_service'
  },
};
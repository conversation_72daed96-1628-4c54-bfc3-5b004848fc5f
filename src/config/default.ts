/*
 * @Description: 默认配置
 * @Author: wangshu<PERSON><EMAIL>
 * @Date: 2021-08-25 10:21:23
 */

import dayjs from 'dayjs';
import path from 'path';

const dateFormat = function () {
  return `[${dayjs().format('YYYY-MM-DD HH:mm:ss:SSS')}]`;
};

export default {
  port: 8992,
  prefix: '/api/icon',
  logger: {
    dev: {
      name: 'dev',
      level: 'debug',
      json: false,
      colorize: 'all',
      localTime: true,
      label: process.pid,
      timestamp: dateFormat
    },
    prod: {
      name: 'prod',
      level: 'info',
      json: false,
      colorize: false,
      localTime: true,
      label: process.pid,
      timestamp: dateFormat,
      // DailyRotateFile配置
      datePattern: 'YYYY-MM-DD-HH',
      filename: path.join(__dirname, '../../', 'logs', '%DATE%.log'),
      // dirname: '/',
      maxFiles: '60d'
    }
  },
  mysql: {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: '123456',
    database: 'bce_icon_service'
  },
  bos: {
    ak: '97e63c9dbc47432eb4f033fadae78ad3',
    sk: 'd2e5920e283248f3b0aa444f7f7c753d',
    endpoint: 'http://bj.bcebos.com',
    bucket: 'console-center',
    region: 'bj.bcebos.com'
  },
  fanyi: {
    appId: '20171123000098989',
    key: 'n72U0FyDKzRguPdt5PTl',
    host: 'http://api.fanyi.baidu.com'
  },
  uuap: {
    protocol: 'https:',
    hostname: 'uuap.baidu.com',
    port: '443',
    loginMethod: '/login',
    logoutMethod: '/logout',
    sessionValidateUrl: '/session/validate',
    sTokenDecryptMethod: '/sTokenDecrypt',
    appKey: 'uuapclient-6-zgXaWmA27L0mQYmabte3',
    secretKey: 'b2f9e27bc8794dc0b947ad',
    version: 'v2',
    pTokenName: 'UUAP_P_TOKEN',
    sTokenName: 'UUAP_S_TOKEN'
  },
  PRIMARY_KEY: 'bd8743bf02j9382bc0jjddh382dfj20advrg83hc923'
};

/*
 * @Description:
 * @Author:
 * @Date: 2021-08-26 19:14:24
 */

import joi from 'joi';

export default {
  querySchema: {
    query: joi.object({
      id: [
        joi.string(),
        joi.number(),
        joi.required()
      ]
    })
  },
  paramSchema: {
    params: joi.object({
      userId: joi.number().required()
    })
  },
  bodySchema: {
    body: joi.object({
      name: joi.string().required(),
      password: joi.number().required()
    })
  },
  headersSchema: {
    headers: {
      'Content-Type': joi.string().required()
    }
  }
};
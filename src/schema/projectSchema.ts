/*
 * @Description: project相关校验
 * @Author: <EMAIL>
 * @Date: 2021-08-31 13:35:04
 */

import joi from 'joi';

export default {
  getProjectSchema: {
    params: joi.object({
      id: [
        joi.string(),
        joi.number(),
        joi.required()
      ]
    })
  },
  createProjectSchema: {
    params: joi.object({
      name: [
        joi.string(),
        joi.required()
      ],
      description: [
        joi.string()
      ]
    })
  }
};
/*
 * @Description: icon服务相关参数校验
 * @Author: wang<PERSON><EMAIL>
 * @Date: 2021-08-31 16:56:10
 */


import joi from 'joi';

export default {
  uploadSvgSchema: {
    files: joi.object({
      file: joi.any().required()
    }),
    body: joi.object({
      type: joi.string()
    })
  },
  bindSchema: {
    body: joi.object({
      projectId: joi.number().required(),
      typeId: joi.number().required(),
      iconList: joi.array().items({
        name: joi.string().max(40).required(),
        enName: joi.string().max(40).required(),
        icon: joi.string().required()
      })
    })
  },
  iconlistSchema: {
    body: joi.object({
      projectId: joi.number(),
      typeId: joi.number(),
      name: joi.string().allow(''),
      pageNo: joi.number(),
      pageSize: joi.number()
    })
      .with('typeId', 'projectId')
  },
  downloadSchema: {
    body: joi.object({
      ids: joi.array().items(joi.string(), joi.number()).required(),
      size: joi.number(),
      strokeWidth: joi.number(),
      color: joi.string().regex(/^#?([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$/),
      type: joi.valid(0, 1),
      strokeLinecap: joi.valid(0, 1, 2),
      strokeLinejoin: joi.valid(0, 1, 2)
    })
  },
  batchDelSchema: {
    body: joi.object({
      ids: joi.array().items(joi.string(), joi.number()).required(),
      projectId: joi.number().required()
    })
  },
  iconEditParam: {
    body: joi.object({
      projectId: joi.number().required(),
      iconId: joi.number().required(),
      typeId: joi.number().required(),
      name: joi.string().required(),
      enName: joi.string().required()
    })
  }
};
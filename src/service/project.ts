/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2021-08-31 13:07:53
 */
import {Project, Type, UserRole, Icon} from '../model/index';
import {aclMap} from './acl';
import seq from '../model/db';

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export default class ProjectService {
  static async getProject(projectId: number, userId: number) {
    let [res, types] = await Promise.all([
      Project.findOne({
        where: {
          id: projectId
        },
        raw: true
      }),
      Type.findAll({
        where: {
          project_id: projectId
        },
        order: ['parent_id', 'sort'],
        raw: true
      })

    ]);
    const acl = await this.getUserAcl(userId, projectId);

    if (res) {
      return {
        ...res,
        types,
        acl
      };
    }
    return '该项目不存在';
  }

  static async projectList(userId: number, needAcl: boolean = false, limitRole: number = 3) {
    try {
      const projectIds = await UserRole.findAll({
        where: {
          user_id: userId,
          role: [0, 1, 2, 3].filter(role => role <= (typeof limitRole === 'number' ? limitRole : 3))
        }
      });

      if (!projectIds || !projectIds.length) {
        return [];
      }

      const list = await Project.findAll({
        where: {
          id: projectIds.map(item => item.project_id),
          deleted: false
        },
        include: Type
      });

      list.map(item => {
        item.types = item.types.sort((a: any, b: any) => {
          if (a.parent_id === b.parent_id) {
            return a.sort - b.sort;
          }
          return a.parent_id - b.parent_id;
        });
        return item;
      });

      if (needAcl) {
        const ids = list.map(item => item.id);

        const acls = await Promise.all(ids.map(projectId => this.getUserAcl(userId, projectId)));

        return list.map((item: any, index: number) => {
          item.dataValues.acl = acls[index];
          return item;
        });
      }

      return list;
    }
    catch (err: any) {
      throw new Error(err);
    }
  }

  // 创建项目
  static async createProject(userId: number, params: any) {
    // 创建项目, 拿到新项目 ID
    const {id: newProjectId} = await Project.create({
      name: params.name,
      description: params.description
    });

    // 拿到项目ID，创建权限关系和一个默认分组
    if (newProjectId) {
      await Promise.all([
        // 创建默认权限，自己为超级管理员
        UserRole.create({
          project_id: newProjectId,
          user_id: userId,
          role: 0
        }),
        // 创建一个默认分组，不然没发上传图标
        Type.create({
          project_id: newProjectId,
          name: '默认分组',
          iconType: 0,
          sort: 1
        })
      ]);

      return {
        projectId: newProjectId
      };
    }

    return '项目创建失败';
  }

  // 编辑项目信息
  static async editProject(projectId: number, params: any) {
    if (!projectId) {
      return '没有找到项目';
    }

    const res = await Project.update({
      name: params.name,
      description: params.description
    }, {
      where: {
        id: projectId
      }
    });

    if (res) {
      return '更新成功';
    }
    throw new Error('更新失败');
  }

  // 删除项目
  static async deleteProject(projectId: number) {

    // 官方项目不可删除
    if ([1, 2].includes(projectId)) {
      throw new Error('该项目不可删除！');
    }

    const [delProject, delIcon, delType, delRole] = await Promise.all([
      // 删除项目
      Project.update({
        deleted: true
      }, {
        where: {
          id: projectId
        }
      }),
      // 删除图标
      Icon.update({
        deleted: 1
      }, {
        where: {
          projectId
        }
      }),
      // 删除分类
      Type.destroy({
        where: {
          project_id: projectId
        }
      }),
      // 删除权限
      UserRole.destroy({
        where: {
          project_id: projectId
        }
      })
    ]);

    return {
      delProject,
      delIcon,
      delType,
      delRole
    };
  }

  // 获取项目的权限用户列表
  static async getProjectInfoWithPermission(projectId: number, userId: number, {
    pageNo,
    pageSize
  }: any) {
    pageNo = Number(pageNo || 0) < 1 ? 1 : Number(pageNo);
    pageSize = Number(pageSize) || 8;

    const start = Number(pageNo - 1) * pageSize;

    const [roleList] = await seq.query(`
      select u.id, r.role, u.username, u.name
      from user_role r inner join user u
      on r.user_id = u.id
      where r.project_id = :projectId
      order by r.user_id <> :userId, r.role
      limit :offetStart, :pageSize
    `, {
      replacements: {
        projectId,
        userId,
        pageSize,
        offetStart: start
      }
    });

    const [allListCounts] = await seq.query(`
      select 1
      from user_role r inner join user u
      on r.user_id = u.id
      where r.project_id = :projectId
    `, {
      replacements: {
        projectId
      }
    });

    if (roleList && roleList.length) {

      roleList.find((item: any) => {
        if (item.id === userId) {
          item.isOwn = true;
          return true;
        }
      });

      return {
        list: roleList,
        total: allListCounts.length
      };
    }
    return '该项目不存在';
  }

  // 添加用户权限
  static async addPermission(userId: number, projectId: number, params: any) {
    // 是否已经添加
    const isAdded = await UserRole.findOne({
      where: {
        project_id: projectId,
        user_id: params.addUserId
      }
    });

    if (isAdded) {
      throw new Error('该用户已存在，勿重复添加');
    }

    const res = await UserRole.create({
      project_id: projectId,
      user_id: params.addUserId,
      role: params.role
    });

    return res;
  }

  // 删除用户权限
  static async deletePermission(userId: number, projectId: number, deleteUserId: number) {
    const res = await UserRole.destroy({
      where: {
        project_id: projectId,
        user_id: deleteUserId
      }
    });

    if (res) {
      return 'success';
    }
    throw new Error('删除失败');
  }

  // 编辑用户权限
  static async editPermission(userId: number, projectId: number, {
    editUserId,
    role,
    type
  }: any) {

    // 转让超管
    if (type && type === 'transfer' && editUserId) {

      const res = await Promise.all([
        // 先给对方超级管理
        UserRole.update({
          role: 0
        }, {
          where: {
            project_id: projectId,
            user_id: editUserId
          }
        }),
        // 收回自己超管
        UserRole.update({
          role: 2
        }, {
          where: {
            project_id: projectId,
            user_id: userId
          }
        })
      ]);

      return res;
    }
    else if (editUserId && role !== undefined) {
      const res = await UserRole.update({
        role
      }, {
        where: {
          project_id: projectId,
          user_id: editUserId
        }
      });

      if (res && res.length) {
        return 'success';
      }
      throw new Error('权限编辑失败');
    }
    throw new Error('缺少必要参数');
  }

  static async addType(userId: number, projectId: number, typeName: string, iconType: number, parentId: number) {
    // 是否已经添加
    const isAdded = await Type.findOne({
      where: {
        project_id: projectId,
        name: typeName,
        parent_id: parentId
      }
    });

    if (isAdded) {
      throw new Error('名称已存在');
    }

    const lastSort: number = await Type.max('sort', {
      where: {
        project_id: projectId,
        parent_id: parentId
      }
    });

    const res = await Type.create({
      project_id: projectId,
      name: typeName,
      iconType: iconType,
      sort: lastSort + 1,
      parent_id: parentId
    });

    return res;
  }

  static async deleteType(userId: number, projectId: number, typeId: number) {
    const allType = await Type.findAll({
      where: {
        parent_id: typeId
      }
    });

    const childrenTypes = await Type.findAll({
      where: {
        parent_id: allType.map(item => item.id)
      }
    });
    const allTypeIds = [typeId, ...allType.map(item => item.id), ...childrenTypes.map(item => item.id)];

    const [deleteIcons, deleteType] = await Promise.all([
      Icon.update({
        deleted: 1
      }, {
        where: {
          projectId,
          typeId: allTypeIds
        }
      }),
      Type.destroy({
        where: {
          id: allTypeIds,
          project_id: projectId
        }
      })
    ]);

    return {deleteIcons, deleteType};
  }

  static async editType(userId: number, projectId: number, typeId: number, typeName: string) {
    const res = await Type.update({
      name: typeName
    }, {
      where: {
        project_id: projectId,
        id: typeId
      }
    });

    return res;
  }

  static async sortType(userId: number, projectId: number, typeId: number, sort: number) {
    const onType = await Type.findOne({
      where: {
        id: typeId
      }
    });
    const list = await Type.findAll({
      where: {
        project_id: projectId,
        parent_id: onType?.parent_id
      },
      order: ['sort']
    });
    const index = list.findIndex(item => item.id === typeId);
    if (index === 0 && sort < 0) {
      throw new Error('已经是第一条，不能再上移');
    }
    if (index === list.length - 1 && sort > 0) {
      throw new Error('已经是最后一条，不能再下移');
    }
    const otherType = list[index + sort];
    const res = Promise.all([
      Type.update({
        sort: otherType.sort
      }, {
        where: {
          id: typeId
        }
      }),
      Type.update({
        sort: list[index].sort
      }, {
        where: {
          id: otherType.id
        }})
    ]);
    return res;
  }

  private static async getUserAcl(userId: number | undefined, projectId: number) {

    if (!userId) {
      return {};
    }

    const userRole = await UserRole.findOne({
      where: {
        project_id: projectId,
        user_id: userId
      },
      attributes: ['role']
    });

    let acl: any = aclMap.get(userRole?.role);

    // 拷贝一份，不然会改变源数据
    let userAcl = acl && Object.assign({}, acl);

    if (!userAcl && (projectId === 1 || projectId === 2)) {
      userAcl = {
        read: true,
        download: true
      };
    }

    // 官方图标库，不可以删除
    if ((projectId === 1 || projectId === 2) && userAcl.projectDelete) {
      userAcl.projectDelete = false;
    }

    return userAcl;
  }
}
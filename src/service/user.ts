/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2021-08-27 15:48:58
 */

import {User} from '../model/index';
import {Op} from 'sequelize';

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export default class UserService {
  static async getUserInfo(userInfo: UserInfo) {
    try {
      UserService.addUserInfo(userInfo);
    }
    finally {
      return userInfo;
    }
  }

  static async addUserInfo(userInfo: UserInfo) {
    await User.findOrCreate({
      where: {
        email: userInfo.email
      },
      defaults: {
        name: userInfo.name,
        username: userInfo.username,
        email: userInfo.email
      }
    });
  }

  // 搜索用户，通过 name 或者 username 模糊搜索
  static async searchUser(userId: number, search: string) {

    const res = await User.findAll({
      where: {
        [Op.or]: [
          {
            username: {
              [Op.substring]: search
            }
          },
          {
            name: {
              [Op.substring]: search
            }
          }
        ],
        id: {
          [Op.not]: userId
        }
      },
      limit: 10
    });

    return res;
  }
};
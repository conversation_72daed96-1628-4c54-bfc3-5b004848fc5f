/* eslint-disable @typescript-eslint/member-ordering */
/*
 * @Description: icon服务相关
 * @Author: <EMAIL>
 * @Date: 2021-08-31 16:18:57
 */
import {File} from 'formidable';
// import {bosClient} from '../common/bos-client';
// import {redisClient} from '../common/redis';
// import {v4} from 'uuid';
import {getTranslate} from '../common/api';
import AdmZip from 'adm-zip';
import {parseSvg} from '../common/parse-svg';
import {Icon, Type} from '../model';
import {Op} from 'sequelize';
import {stringify} from 'svgson';
import {AnyObject} from '../types/baseType';
import ComposeSvg, {IconType, StrokeLinecapType, StrokeLinejoinType} from '../common/compose-svg';
import {typeMap, strokeLinecapMap, strokeLinejoinMap} from '../util/map';
import {transCamelCase} from '../util/util';
import {iconUpload, iconBind} from '../decorators/icon';
import {checkAcl} from './acl';
// import { cond } from 'lodash';

export interface IconUpload {
  file?: File;
}

interface IconInfo {
  name: string;
  enName: string;
  path?: string;
  svgjson: string;
  // icon内容
  icon: string;
  parseSuccess: boolean;
};

export interface BindParam {
  projectId: number;
  typeId: number;
  iconList: IconInfo[];
  type: 'png' | 'standardSvg' | 'svg';
};

export interface IconEditParam {
  projectId: number;
  iconId: number;
  typeId: number;
  name: string;
  enName: string;
};


interface IconReturn {
  // 英文名称
  enName?: string;
  // 文件名称，翻译需要
  fileName?: string;
  // 完整名称
  name: string;
  icon: string;
  // 是否解析成功
  parseSuccess: boolean;
}

export interface IconlistParam {
  // 项目id
  projectId?: number;
  // 分类id
  typeId?: number;
  // 查询名称
  name?: string;
  pageNo?: number;
  pageSize?: number;
}

export interface IconDownParam {
  // icon id
  ids: number[];
}

export interface IconSvgDownParam extends IconDownParam{
  // 图标大小
  size: number;
  // 线段粗细
  strokeWidth: number;
  color: string;
  // 图标风格 0 线性 1 面性
  type: 0 | 1;
  // 端点类型 0：圆角 1：平角 2：方角
  strokeLinecap: 0 | 1 | 2;
  // 拐点类型 0：圆角 1：平角 2：方角
  strokeLinejoin: 0 | 1 | 2;
}

export interface IconBatchDeleteParam {
  ids: number[];
  projectId: number;
}

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export default class IconService {
  @iconUpload('svg+xml')
  static async standardSvgUpload(payload: IconUpload, buffer?: Buffer) {
    if (payload && buffer) {
      const {svgjson, success} = await parseSvg(buffer.toString());
      return {
        parseSuccess: success,
        icon: 'data:image/svg+xml;base64,' + Buffer.from(stringify(svgjson)).toString('base64')
      };
    }
  }
  @iconUpload('png')
  static async pngUpload(payload: IconUpload, buffer?: Buffer) {
    if (payload && buffer) {
      return {
        parseSuccess: true,
        icon: 'data:image/png;base64,' + buffer.toString('base64')
      };
    }
  }

  static async zipUpload(payload: IconUpload) {
    if (payload.file) {
      const {size, type, path} = payload.file;
      if (type !== 'application/zip') {
        throw new Error('非法zip压缩文件！');
      }
      else if (size > 2000 * 1024 * 1024) {
        throw new Error('文件大小限制20m!');
      }
      else {
        let zipEntries = new AdmZip(path).getEntries();
        let unzipData: IconReturn[] = [];
        for (let file of zipEntries) {
          // 过滤mac环境压缩产生的__MACOSX文件
          if (!file.entryName.includes('__MACOSX/')) {
            let splitArr = file.name.split('.');
            let fileName = splitArr[splitArr.length - 2];
            let type = splitArr[splitArr.length - 1];
            if (type === 'svg') {
              let buffer = file.getData();
              // let resourceId = v4();
              // await redisClient.setStringKey(resourceId, buffer);
              const {svgjson, success} = await parseSvg(buffer.toString());
              unzipData.push({
                fileName,
                name: file.name,
                icon: 'data:image/svg+xml;base64,' + Buffer.from(stringify(svgjson)).toString('base64'),
                parseSuccess: success
              });
            }
          }
        }
        // 因百度翻译接口qps限制,将name组合在一起一次翻译
        let nameSting = unzipData.reduce((total, item) => {
          return total + item.fileName! + '\n';
        }, '');
        const enNameList = await getTranslate(nameSting);
        let enNameArr = enNameList.map((name: string) => transCamelCase(name));
        unzipData = unzipData.map((item, index) => ({
          name: item.fileName!,
          icon: item.icon,
          enName: enNameArr[index] || '',
          parseSuccess: item.parseSuccess
        }));
        return {
          icon: unzipData
        };
      }
    }
  }

  @iconBind()
  static async standardSvgBind(payload: BindParam, userId: number) {
    let {projectId, typeId, iconList, type} = payload;

    // 权限检测
    await checkAcl(projectId, userId, ['iconUpload']);

    const enNames = await Icon.findAll({
      where: {
        projectId
      },
      attributes: ['enName']
    });
    // 转化成对象，方便查询
    const existingEnNamesMap: any = {};
    enNames.forEach(item => {
      existingEnNamesMap[String(item.enName).toLowerCase()] = true;
    });

    let successList = [];
    for (let item of iconList) {
      if (item.name.includes('/')) {
        throw new Error(`${item.name}名称不合法`);
      }
      // 名称重复的，存起来返回
      if (existingEnNamesMap[String(item.enName).toLowerCase()]) {
        throw new Error('此英文名称已经存在');
      }
      if (!this.isIconEnNameValid(item.enName)) {
        throw new Error('此英文名称不合法，必须是两位以上的以字母开头的字母数字组合');
      }
      const svgStrBase64 = item.icon.replace('data:image/svg+xml;base64,', '');
      const buff = Buffer.from(svgStrBase64, 'base64');
      const svgStr = buff.toString('utf-8');
      const {svgjson, path, success} = await parseSvg(svgStr);
      item.path = JSON.stringify(path);
      item.svgjson = JSON.stringify(svgjson);
      item.parseSuccess = success;
      if (!item.parseSuccess) {
        throw new Error(`${item.enName}资源非标准svg`);
      }
      else {
        await Icon.create({
          projectId,
          typeId,
          icon: item.svgjson,
          name: item.name,
          enName: item.enName,
          path: item.path,
          iconType: ({
            'standardSvg': 0,
            'png': 1,
            'svg': 2
          })[type]
        });
        successList.push(item);
      }
    }
    return {
      uploadList: successList
    };
  }

  @iconBind()
  static async pngBind(payload: BindParam, userId: number) {
    let {projectId, typeId, iconList, type} = payload;

    // 权限检测
    await checkAcl(projectId, userId, ['iconUpload']);
    let successList = [];
    for (let item of iconList) {
      if (item.name.includes('/')) {
        throw new Error(`${item.name}名称不合法`);
      }
      if (!this.isIconEnNameValid(item.enName)) {
        throw new Error('此英文名称不合法，必须是两位以上的以字母开头的字母数字组合');
      }
      const icon = item.icon;
      await Icon.create({
        projectId,
        typeId,
        icon: icon,
        name: item.name,
        enName: item.enName,
        iconType: ({
          'standardSvg': 0,
          'png': 1,
          'svg': 2,
          'illustrationPng': 3
        })[type]
      });
      successList.push(item);
    }
    return {
      uploadList: successList
    };
  }

  static async iconlist(payload: IconlistParam) {
    let {projectId, typeId, name, pageNo, pageSize} = payload;
    let condition: AnyObject = {};

    if (name) {
      condition = {
        [Op.or]: [
          {
            name: {
              [Op.like]: `%${name}%`
            }
          },
          {
            enName: {
              [Op.like]: `%${name}%`
            }
          }
        ]
      };
    }
    if (projectId) {
      condition.projectId = projectId;
    }
    if (typeId) {
      condition.typeId = typeId;
      // 查询type及子层级type
      const typeArr = await Type.findAll({
        where: {
          parent_id: typeId
        },
        attributes: ['id']
      });
      const typeIdArr = typeArr.map(item => item.id);
      const childTypeArr = await Promise.all(typeIdArr.map(async item => {
        return await Type.findAll({
          where: {
            parent_id: item
          },
          attributes: ['id']
        });
      }));
      const childIdTypeArr = childTypeArr.map(item => item.map(e => e.id));
      condition.typeId = {
        [Op.or]: [...typeIdArr, ...childIdTypeArr, typeId]
      };
    }

    condition.deleted = 0;

    const {count, rows} = await Icon.findAndCountAll({
      where: condition,
      attributes: ['id', 'projectId', 'typeId', 'name', 'enName', 'path', 'icon', 'iconType'],
      offset: pageNo && pageSize ? (pageNo - 1) * pageSize : 0,
      limit: pageSize,
      order: [['typeId', 'ASC']],
      raw: true
    });
    return {
      pageNo: pageNo,
      pageSize: pageSize || count,
      total: count,
      iconlist: rows
    };
  }

  static async iconDownload(payload: IconDownParam) {
    const {ids} = payload;

    const iconList = await Icon.findAll({
      where: {
        id: ids
      },
      attributes: ['name', 'icon', 'path', 'iconType']
    });
    const zip = new AdmZip();
    for (let icon of iconList) {
      let iconStr = icon.icon;
      let name = icon.name;
      let encoding: BufferEncoding = 'utf-8';
      // 标准svg需要处理
      if (icon.iconType === 0) {
        const {type, size, strokeLinecap, strokeLinejoin, strokeWidth, color} = payload as IconSvgDownParam;
        const typeStr = typeMap.get(type);
        const strokeLinecapStr = strokeLinecapMap.get(strokeLinecap);
        const strokeLinejoinStr = strokeLinejoinMap.get(strokeLinejoin);
        const composer = new ComposeSvg(typeStr as IconType, {
          size,
          strokeWidth,
          strokeLinecap: strokeLinecapStr as StrokeLinecapType,
          strokeLinejoin: strokeLinejoinStr as StrokeLinejoinType,
          color
        });
        let svgPath = JSON.parse(icon.path);
        iconStr = await composer.composeSvg(JSON.parse(iconStr), svgPath);
        name += '.svg';
      }
      else if (icon.iconType === 1) {
        iconStr = iconStr.replace('data:image/png;base64,', '');
        name += '.png';
        encoding = 'base64';
      }

      zip.addFile(name, Buffer.from(iconStr, encoding));
    }
    return zip.toBuffer();
  }
  // 批量删除
  static async batchDelete(payload: IconBatchDeleteParam, userId: number) {
    const {ids, projectId} = payload;

    // 权限检测
    await checkAcl(projectId, userId, ['iconDelete']);

    const query = await Icon.findAll({
      where: {
        id: ids,
        projectId: projectId
      }
    });
    if (query.length !== ids.length) {
      throw new Error('删除失败');
    }
    const res = await Icon.destroy({
      where: {
        id: ids,
        projectId: projectId
      }
    });
    if (res) {
      return '删除成功';
    }
    throw new Error('删除失败');
  }

  static isIconEnNameValid(name: string) {
    return (/^[a-zA-Z][a-zA-Z0-9]*$/).test(name);
  }

  static async editIcon(payload: IconEditParam, userId: number) {
    const {iconId, typeId, name, enName, projectId} = payload;
    // 权限检测
    await checkAcl(projectId, userId, ['iconEdit']);

    if (!this.isIconEnNameValid(enName)) {
      throw new Error('此英文名称不合法，必须是两位以上的以字母开头的字母数字组合');
    }

    const enNameIsRepeat = !!await Icon.findOne({
      where: {
        enName: {
          [Op.like]: enName
        },
        projectId
      }
    });

    if (enNameIsRepeat) {
      throw new Error('英文名称在项目中重复');
    }
    const res = await Icon.update({
      typeId,
      name,
      enName
    }, {
      where: {
        id: iconId
      }
    });
    if (res) {
      return '更新成功';
    }
    throw new Error('更新失败');
  }

  // 从别的项目添加 icon
  static async addIconFromProject(userId: number, targetProjectId: number, typeId: number, ids: number[]) {
    // 拿到需要添加的icon信息，和目标项目已经存在的图标的 enName。以便检测名称重复
    const [icons, enNames] = await Promise.all([
      Icon.findAll({
        where: {
          id: ids
        },
        attributes: ['enName', 'icon', 'iconType', 'name', 'path', 'size']
      }),
      Icon.findAll({
        where: {
          projectId: targetProjectId
        },
        attributes: ['enName']
      })
    ]);

    // 转化成对象，方便查询
    const existingEnNamesMap: any = {};
    enNames.forEach(item => {
      existingEnNamesMap[String(item.enName).toLowerCase()] = true;
    });

    const nameRepeat = [];
    const errorIcons = [];

    for (let icon of icons) {
      // 名称重复的，存起来返回
      if (existingEnNamesMap[String(icon.enName).toLowerCase()]) {
        nameRepeat.push(icon);
      }
      // 不重复的就插库，收集错误
      else {
        try {
          await Icon.create({
            projectId: targetProjectId,
            typeId,
            icon: icon.icon,
            name: icon.name,
            enName: icon.enName,
            path: icon.path,
            iconType: icon.iconType
          });
        }
        catch (e) {
          errorIcons.push({
            error: e,
            icon: icon
          });
        }
      }
    }

    return {
      userId,
      projectId: targetProjectId,
      typeId,
      ids,
      icons,
      enNames,
      nameRepeat,
      errorIcons
    };
  }
}
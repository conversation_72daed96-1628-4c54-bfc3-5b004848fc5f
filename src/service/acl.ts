import {UserRole} from '../model/index';

export const aclMap = new Map([
  [0,
    {
      aclEdit: true,
      projectDelete: true,
      projectEdit: true,
      typeCreate: true,
      typeEdit: true,
      typeDelete: true,
      iconEdit: true,
      iconUpload: true,
      iconDelete: true,
      read: true,
      download: true
    }
  ],
  [1,
    {
      projectEdit: true,
      typeCreate: true,
      typeEdit: true,
      typeDelete: true,
      iconEdit: true,
      iconUpload: true,
      iconDelete: true,
      read: true,
      download: true
    }
  ],
  [2,
    {
      iconEdit: true,
      iconUpload: true,
      iconDelete: true,
      read: true,
      download: true
    }
  ],
  [3,
    {
      read: true,
      download: true
    }
  ],
  [undefined, undefined]
]);

export type acl = 'aclEdit'
  | 'projectDelete'
  | 'projectEdit'
  | 'typeCreate'
  | 'typeEdit'
  | 'typeDelete'
  | 'iconEdit'
  | 'iconUpload'
  | 'iconDelete'
  | 'read'
  | 'download';

export async function checkAcl(projectId: number, userId: number, needAcl: acl[]) {
  const userRole = await UserRole.findOne({
    where: {
      user_id: userId,
      project_id: projectId
    }
  });
  const acl = aclMap.get(userRole?.role);
  if (!acl) {
    throw new Error('没有操作权限');
  }
  for (let item of needAcl) {
    if (!acl[item]) {
      throw new Error('没有操作权限');
    }
  }
}
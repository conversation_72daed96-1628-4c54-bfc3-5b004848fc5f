/*
 * @Description: 参数检查中间件
 * @Author: wangshuhui<PERSON>@baidu.com
 * @Date: 2021-08-26 17:26:23
 */

import {SchemaRule} from '../types/schemaType';
import log from '../common/log';
import {IRequestContext} from '../types/resquestType';

export default (paramSchema: SchemaRule) => {
  return async (ctx: IRequestContext, next: () => Promise<any>) => {
    const reqParam = {
      params: ctx.params,
      query: ctx.query,
      body: ctx.request.body,
      headers: ctx.headers,
      files: ctx.request.files
    };
    ctx.reqParams = reqParam;
    if (!paramSchema) {
      return next();
    }
    let schemaKeys = Object.getOwnPropertyNames(paramSchema) as Array<keyof SchemaRule>;
    if (paramSchema && schemaKeys.length > 0) {
      for (const key of schemaKeys) {
        let result = paramSchema[key]!.validate(reqParam[key], {allowUnknown: true});
        if (result.error) {
          log.warn('[param error]: ', {message: result.error.message});
          // errcode待补充
          ctx.throw(400, new Error(`参数错误:${result.error}`));
        }
        reqParam[key] = result.value;
      }
    }
    await next();
  };
};
/*
 * @Description: 中间件入口
 * @Author: <EMAIL>
 * @Date: 2021-08-24 20:14:03
 */

import Koa from 'koa';
import body from 'koa-body';
import log from '../common/log';
// import fs from 'fs-extra';
import uuap from './uuap';
import logger from './logger';
import router from '../router';
import response from './response';

export default (app: Koa) => {
  app.on('error', err => {
    log.error('[server error]:', {message: err});
  });

  app
    .use(body({
      multipart: true, // 支持文件上传
      formidable: {
        keepExtensions: true, // 保持文件的后缀
        maxFieldsSize: 2000 * 1024 * 1024 // 文件上传大小
      }
    }))
    .use(logger)
    .use(response)
    .use(uuap)
    .use(router.routes())
    .use(router.allowedMethods());
};
/*
 * @Description: 响应格式化中间件
 * @Author: <EMAIL>
 * @Date: 2021-08-26 14:38:50
 */

import log from '../common/log';
import {IRequestContext} from '../types/resquestType';

export default async (ctx: IRequestContext, next: () => Promise<any>) => {
  try {
    await next();
    if (ctx.status === 404) {
      ctx.throw(404, `path '${ctx.path}' not found`);
    }
    if (Buffer.isBuffer(ctx.body) || ctx.type === 'application/zip') {
      return;
    }
    ctx.body = {
      success: true,
      code: 0,
      result: ctx.body
    };
  }
  catch (err: any) {
    ctx.status = err.status || 200;
    log.warn('server warn:', {
      message: JSON.stringify(
        {
          method: ctx.request.method,
          url: ctx.request.originalUrl,
          status: ctx.status,
          msg: err.message
        }, null, '\t')
    });
    log.warn('server warn reqParams:', {message: JSON.stringify(ctx.reqParams, null, '\t')});
    ctx.body = {
      code: err.code || 500,
      success: false,
      content: err.content || null,
      message: err.message
    };
  }
};
/*
 * @Description: route定义
 * @Author: wang<PERSON>hui<PERSON>@baidu.com
 * @Date: 2021-08-26 17:08:44
 */

import Koa, {DefaultState} from 'koa';
import {User} from 'src/model';
import {IRequestContext} from './resquestType';
import {SchemaRule} from './schemaType';

export default interface IRoute {
  method: 'get' | 'post' | 'put' | 'delete';
  path: string;
  controller: Koa.Middleware<DefaultState, IRequestContext> | ((ctx: IRequestContext, userInfo: User) => Promise<void>);
  schema?: SchemaRule;
  permission?: Koa.Middleware;
}
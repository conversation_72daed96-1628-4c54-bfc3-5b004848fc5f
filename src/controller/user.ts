/*
 * @Description: 用户信息
 * @Author: <EMAIL>
 * @Date: 2021-08-27 10:37:17
 */

import {IRequestContext} from '../types/resquestType';
import userService from '../service/user';
import config from '../config';
import {user} from '../decorators/user';
import url from 'url';

const uuapConfig = config.uuap;
// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export default class UserController {
  static async getUser(ctx: IRequestContext) {
    const userInfo = ctx.userInfo;
    const referer = String(ctx?.request?.header?.referer || '');

    // 云舍来源的，不验证登陆
    if (referer && (referer.includes('yunshe.baidu-int.com') || referer.includes('sandbox090.gzns.baidu.com'))) {
      ctx.body = {
        username: '游客'
      };
      return;
    }
    ctx.body = await userService.getUserInfo(userInfo);
  }
  static async logout(ctx: IRequestContext) {
    // 清除cookie
    ctx.cookies.set('UUAP_S_TOKEN', '', {
      domain: 'sandbox.bce.console.baidu-int.com',
      maxAge: 0,
      httpOnly: true
    });

    const redirectUrl = url.format({
      protocol: uuapConfig.protocol,
      host: uuapConfig.hostname,
      pathname: uuapConfig.logoutMethod,
      query: {
        service: 'http://sandbox.bce.console.baidu-int.com/icon/',
        appKey: uuapConfig.appKey
      }
    });
    ctx.body = redirectUrl;
  }

  @user()
  static async search(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {search} = ctx.reqParams.query as any;
    ctx.body = await userService.searchUser(userInfo.id, search);
  }
};
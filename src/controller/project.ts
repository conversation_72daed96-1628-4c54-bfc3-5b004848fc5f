/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2021-08-31 13:28:28
 */

import {IRequestContext} from '../types/resquestType';
import projectService from '../service/project';
import {user} from '../decorators/user';
import {User as UserInfo} from '../model/index';

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export default class ProjectCtrl {
  @user()
  static async getProject(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {id: projectId} = ctx.reqParams.params;
    ctx.body = await projectService.getProject(Number(projectId), userInfo.id);
  }

  @user()
  static async getProjectList(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {
      needAcl,
      limit
    } = ctx.reqParams.query as any;
    let limitRole = isNaN(Number(limit)) ? 3 : Number(limit);
    ctx.body = await projectService.projectList(userInfo.id, !!needAcl, limitRole);
  }

  @user()
  static async getProjectInfoWithPermission(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {id: projectId} = ctx.reqParams.params;
    const {pageNo, pageSize} = ctx.reqParams.query as any;
    ctx.body = await projectService.getProjectInfoWithPermission(Number(projectId), userInfo.id, {
      pageNo,
      pageSize
    });
  }

  @user()
  static async createProject(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {
      name,
      description
    } = ctx.reqParams.body as any;
    ctx.body = await projectService.createProject(userInfo.id, {name, description});
  }

  @user()
  static async editProject(
    ctx: IRequestContext
  ) {
    const {
      name,
      description
    } = ctx.reqParams.body as any;
    const {id: projectId} = ctx.reqParams.params;
    ctx.body = await projectService.editProject(Number(projectId), {name, description});
  }

  @user()
  static async deleteProject(
    ctx: IRequestContext
  ) {
    const {id: projectId} = ctx.reqParams.params;
    ctx.body = await projectService.deleteProject(Number(projectId));
  }

  @user()
  static async addPermissionForProject(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {
      userId: addUserId,
      role
    } = ctx.reqParams.body as any;
    const {id: projectId} = ctx.reqParams.params;
    ctx.body = await projectService.addPermission(userInfo.id, Number(projectId), {addUserId, role});
  }

  @user()
  static async deletePermissionForProject(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {
      userId: deleteUserId
    } = ctx.reqParams.body as any;
    const {id: projectId} = ctx.reqParams.params;
    ctx.body = await projectService.deletePermission(userInfo.id, Number(projectId), deleteUserId);
  }

  @user()
  static async editPermissionForProject(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {
      userId: editUserId,
      role,
      type
    } = ctx.reqParams.body as any;
    const {id: projectId} = ctx.reqParams.params;
    ctx.body = await projectService.editPermission(userInfo.id, Number(projectId), {type, editUserId, role});
  }

  @user()
  static async addTypeForProject(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {
      typeName,
      iconType,
      parentId = null
    } = ctx.reqParams.body as any;
    const {id: projectId} = ctx.reqParams.params;
    ctx.body = await projectService.addType(userInfo.id, Number(projectId), typeName, iconType, parentId);
  }

  @user()
  static async deleteTypeForProject(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {
      typeId
    } = ctx.reqParams.body as any;
    const {id: projectId} = ctx.reqParams.params;
    ctx.body = await projectService.deleteType(userInfo.id, Number(projectId), typeId);
  }

  @user()
  static async editTypeForProject(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {
      typeId,
      typeName
    } = ctx.reqParams.body as any;
    const {id: projectId} = ctx.reqParams.params;
    ctx.body = await projectService.editType(userInfo.id, Number(projectId), typeId, typeName);
  }

  @user()
  static async sortTypeForProject(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {
      typeId,
      sort
    } = ctx.reqParams.body as any;
    const {id: projectId} = ctx.reqParams.params;
    ctx.body = await projectService.sortType(userInfo.id, Number(projectId), typeId, sort);
  }
}
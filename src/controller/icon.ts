/*
 * @Description: icon controller
 * @Author: <EMAIL>
 * @Date: 2021-08-30 23:21:28
 */

import {IRequestContext} from '../types/resquestType';
import type {
  BindParam,
  IconBatchDeleteParam,
  IconDownParam,
  IconlistParam,
  IconEditParam
} from '../service/icon';
import iconService from '../service/icon';
import {user} from '../decorators/user';
import {User as UserInfo} from '../model/index';

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export default class IconCtrl {
  static async upload(ctx: IRequestContext) {
    const file = ctx.request.files!;
    if (ctx.request.body?.type === 'png') {
      ctx.body = await iconService.pngUpload(file);
    }
    else {
      ctx.body = await iconService.standardSvgUpload(file);
    }
  }
  static async zipUpload(ctx: IRequestContext) {
    const file = ctx.request.files!;
    ctx.body = await iconService.zipUpload(file);
  }

  static async iconlist(ctx: IRequestContext) {
    const param = ctx.reqParams.body as IconlistParam;
    ctx.body = await iconService.iconlist(param);
  }

  static async iconDownload(ctx: IRequestContext) {
    const param = ctx.reqParams.body as IconDownParam;
    const data = await iconService.iconDownload(param);
    ctx.type = 'application/zip';
    ctx.attachment('icons.zip');
    ctx.body = data;
  }

  /**
   * 存入图标
   */
  @user()
  static async bind(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const param = ctx.reqParams.body as BindParam;
    if (['png', 'illustrationPng'].includes(param.type)) {
      ctx.body = await iconService.pngBind(param, userInfo.id);
    }
    else {
      ctx.body = await iconService.standardSvgBind(param, userInfo.id);
    }
  }

  /**
   * 删除图标
   */
  @user()
  static async batchDelete(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const param = ctx.reqParams.body as IconBatchDeleteParam;
    ctx.body = await iconService.batchDelete(param, userInfo.id);
  }

  /**
   * 编辑图标
   */
  @user()
  static async editIcon(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const param = ctx.reqParams.body as IconEditParam;
    ctx.body = await iconService.editIcon(param, userInfo.id);
  }

  /**
   * 从别的项目添加
   */
  @user()
  static async fromProjectAdd(
    ctx: IRequestContext,
    userInfo: UserInfo
  ) {
    const {
      projectId,
      typeId,
      ids
    } = ctx.reqParams.body as any;
    ctx.body = await iconService.addIconFromProject(userInfo.id, projectId, typeId, ids);
  }
}
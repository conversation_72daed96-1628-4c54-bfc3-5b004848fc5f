CREATE TABLE `project` (
`id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
`name` VARCHAR(255) NOT NULL COMMENT '项目名称',
`description` VARCHAR(255) DEFAULT NULL COMMENT '项目描述',
`deleted` TINYINT(1) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除',
`create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
PRIMARY KEY(`id`)
) ENGINE=INNODB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

CREATE TABLE `type` (
`id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
`name` VARCHAR(64) NOT NULL COMMENT '分类名称',
`project_id` BIGINT(20) UNSIGNED COMMENT '所属项目id',
`parent_id` BIGINT(20) UNSIGNED NULL COMMENT '父节点id',
`iconType` TINYINT(1) UNSIGNED DEFAULT 0 NOT NULL COMMENT '图标类型',
`sort` BIGINT(20) UNSIGNED NULL COMMENT '排序',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

CREATE TABLE `icon` (
`id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
`project_id` BIGINT(20) UNSIGNED NOT NULL COMMENT '所属项目id',
`type_id` BIGINT(20) UNSIGNED NOT NULL COMMENT '所属分类id',
`name` VARCHAR(64) NOT NULL COMMENT '名称,用于bos查询',
`en_name` VARCHAR(64) NOT NULL COMMENT '英文名称，在所属项目中唯一',
`icon` LONGTEXT NOT NULL COMMENT 'icon',
`path` LONGTEXT COMMENT '解析用于组装的路径',
`size` INT(10) UNSIGNED DEFAULT 16 COMMENT '图标大小',
`color` VARCHAR(255) COMMENT '图标颜色',
`type` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '图标类型 0: 线性 1:面性',
`stroke_width` INT(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '线段粗细',
`stroke_linecap` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '端点类型 0：round(圆角) 1：butt(平角) 2：square(方角)',
`stroke_linejoin` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拐点类型 0：圆角 1：平角 2：方角',
`deleted` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
`create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
PRIMARY KEY(`id`),
INDEX name_index(name)
) ENGINE=INNODB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

-- 初始图标库
INSERT INTO project (name) VALUES ('行业图标库'), ('通用图标库');

-- 初始项目
INSERT INTO
type (name, project_id)
VALUES
('基础', 1),
('方向', 1),
('操作', 1),
('数据', 1),
('文件', 1),
('提示', 1),
('其他', 1);
('公有云', 2),
('工业', 2),
('AI', 2),
('医疗', 2),
('智慧城市', 2),
('金融', 2),
('客服', 2),